<!DOCTYPE html>
<html lang="vi">
<head>
    <meta charset="utf-8">
    <meta name="viewport" content="width=device-width, initial-scale=1">
    <title>Đ<PERSON>ng <PERSON>p - Shop Thời Trang Nh<PERSON>n</title>
    <meta name="description" content="Đăng nhập vào tà<PERSON> khoản Shop Thời Trang để trải nghiệm mua sắm tuyệt vời với phong cách Nhậ<PERSON> Bản">

    <!-- Google Fonts -->
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Noto+Sans+JP:wght@300;400;500;600;700&family=Roboto:wght@300;400;500;600;700&display=swap" rel="stylesheet">

    <!-- Bootstrap 5 -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.3/dist/css/bootstrap.min.css" rel="stylesheet">

    <!-- Font Awesome -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">

    <!-- Japanese Theme CSS -->
    <link type="text/css" rel="stylesheet" href="{{ asset('css/japanese-theme.css') }}" />
</head>

<body class="auth-body-japanese">
    <div class="auth-container-japanese">
        <div class="auth-wrapper">
            <!-- Left Side - Branding -->
            <div class="auth-branding">
                <div class="branding-content">
                    <div class="logo-section">
                        <a href="{{ route('home.index') }}" class="brand-logo">
                            <img src="{{ asset('img/logo.png') }}" alt="Shop Thời Trang" class="logo-img">
                            <span class="brand-text">Shop Thời Trang</span>
                        </a>
                    </div>
                    <div class="branding-text">
                        <h2 class="welcome-title">Chào Mừng Trở Lại</h2>
                        <p class="welcome-subtitle">Đăng nhập để khám phá bộ sưu tập thời trang Nhật Bản độc đáo</p>
                    </div>
                    <div class="branding-features">
                        <div class="feature-item">
                            <i class="fas fa-shield-alt"></i>
                            <span>Bảo mật cao</span>
                        </div>
                        <div class="feature-item">
                            <i class="fas fa-shipping-fast"></i>
                            <span>Giao hàng nhanh</span>
                        </div>
                        <div class="feature-item">
                            <i class="fas fa-medal"></i>
                            <span>Chất lượng đảm bảo</span>
                        </div>
                    </div>
                </div>
            </div>

            <!-- Right Side - Login Form -->
            <div class="auth-form-section">
                <div class="form-container">
                    <div class="form-header">
                        <h1 class="form-title">Đăng Nhập</h1>
                        <p class="form-subtitle">Nhập thông tin để truy cập tài khoản của bạn</p>
                    </div>

                    <!-- Alert Messages -->
                    @if (Session::has('message'))
                        <div class="alert alert-danger alert-dismissible fade show" role="alert">
                            <i class="fas fa-exclamation-triangle me-2"></i>
                            {{ Session::get('message') }}
                            <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
                        </div>
                        @php Session::put('message', null); @endphp
                    @endif

                    <form action="{{ route('user.cus_login') }}" method="post" id="loginForm" class="auth-form">
                        @csrf

                        <!-- Email Field -->
                        <div class="form-group">
                            <label for="email" class="form-label">
                                <i class="fas fa-envelope me-2"></i>
                                Email
                            </label>
                            <input
                                type="email"
                                name="email"
                                id="email"
                                class="form-control @error('email') is-invalid @enderror"
                                placeholder="Nhập địa chỉ email của bạn"
                                value="{{ old('email') }}"
                                required
                                autocomplete="email">
                            @error('email')
                                <div class="invalid-feedback">
                                    <i class="fas fa-exclamation-circle me-1"></i>
                                    {{ $message }}
                                </div>
                            @enderror
                        </div>

                        <!-- Password Field -->
                        <div class="form-group">
                            <label for="password" class="form-label">
                                <i class="fas fa-lock me-2"></i>
                                Mật khẩu
                            </label>
                            <div class="password-input-wrapper">
                                <input
                                    type="password"
                                    name="password"
                                    id="password"
                                    class="form-control @error('password') is-invalid @enderror"
                                    placeholder="Nhập mật khẩu của bạn"
                                    required
                                    autocomplete="current-password">
                                <button type="button" class="password-toggle" onclick="togglePassword()">
                                    <i class="fas fa-eye" id="passwordToggleIcon"></i>
                                </button>
                            </div>
                            @error('password')
                                <div class="invalid-feedback">
                                    <i class="fas fa-exclamation-circle me-1"></i>
                                    {{ $message }}
                                </div>
                            @enderror
                        </div>

                        <!-- Remember Me & Forgot Password -->
                        <div class="form-options">
                            <div class="form-check">
                                <input class="form-check-input" type="checkbox" id="remember" name="remember">
                                <label class="form-check-label" for="remember">
                                    Ghi nhớ đăng nhập
                                </label>
                            </div>
                            <a href="#" class="forgot-password-link" onclick="alert('Chức năng quên mật khẩu đang được phát triển.'); return false;">
                                Quên mật khẩu?
                            </a>
                        </div>

                        <!-- Submit Button -->
                        <button type="submit" class="btn-japanese btn-primary btn-login">
                            <i class="fas fa-sign-in-alt me-2"></i>
                            Đăng Nhập
                        </button>

                        <!-- Register Link -->
                        <div class="auth-switch">
                            <p class="switch-text">Bạn chưa có tài khoản?</p>
                            <a href="{{ route('user.cus_register') }}" class="switch-link">
                                Tạo tài khoản mới
                                <i class="fas fa-arrow-right ms-1"></i>
                            </a>
                        </div>

                        <!-- Back to Home -->
                        <div class="back-to-home">
                            <a href="{{ route('home.index') }}" class="home-link">
                                <i class="fas fa-home me-2"></i>
                                Quay lại trang chủ
                            </a>
                        </div>
                    </form>
                </div>
            </div>
        </div>
    </div>

    <!-- Custom CSS -->
    <style>
    /* ===== JAPANESE AUTH STYLES ===== */
    .auth-body-japanese {
        font-family: var(--font-primary);
        background: linear-gradient(135deg, var(--light-gray) 0%, var(--white) 50%, var(--light-gray) 100%);
        min-height: 100vh;
        display: flex;
        align-items: center;
        justify-content: center;
        padding: var(--spacing-md);
    }

    .auth-container-japanese {
        width: 100%;
        max-width: 1200px;
        margin: 0 auto;
    }

    .auth-wrapper {
        background: var(--white);
        border-radius: var(--radius-xl);
        box-shadow: var(--shadow-xl);
        overflow: hidden;
        display: grid;
        grid-template-columns: 1fr 1fr;
        min-height: 600px;
    }

    /* Left Side - Branding */
    .auth-branding {
        background: linear-gradient(135deg, var(--primary-red) 0%, var(--primary-blue) 100%);
        color: var(--white);
        padding: var(--spacing-3xl);
        display: flex;
        flex-direction: column;
        justify-content: center;
        position: relative;
        overflow: hidden;
    }

    .auth-branding::before {
        content: '';
        position: absolute;
        top: -50%;
        right: -50%;
        width: 200%;
        height: 200%;
        background: url('data:image/svg+xml,<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 100 100"><circle cx="50" cy="50" r="2" fill="rgba(255,255,255,0.1)"/></svg>') repeat;
        animation: float 20s infinite linear;
    }

    @keyframes float {
        0% { transform: translateX(0) translateY(0); }
        100% { transform: translateX(-50px) translateY(-50px); }
    }

    .branding-content {
        position: relative;
        z-index: 2;
    }

    .logo-section {
        margin-bottom: var(--spacing-2xl);
    }

    .brand-logo {
        display: flex;
        align-items: center;
        gap: var(--spacing-md);
        text-decoration: none;
        color: var(--white);
    }

    .logo-img {
        height: 60px;
        width: auto;
        filter: brightness(0) invert(1);
    }

    .brand-text {
        font-size: var(--font-size-2xl);
        font-weight: 700;
    }

    .welcome-title {
        font-size: var(--font-size-3xl);
        font-weight: 700;
        margin-bottom: var(--spacing-md);
        line-height: 1.2;
    }

    .welcome-subtitle {
        font-size: var(--font-size-lg);
        opacity: 0.9;
        line-height: 1.6;
        margin-bottom: var(--spacing-2xl);
    }

    .branding-features {
        display: flex;
        flex-direction: column;
        gap: var(--spacing-lg);
    }

    .feature-item {
        display: flex;
        align-items: center;
        gap: var(--spacing-md);
        font-size: var(--font-size-base);
        opacity: 0.9;
    }

    .feature-item i {
        font-size: var(--font-size-lg);
        width: 24px;
    }

    /* Right Side - Form */
    .auth-form-section {
        padding: var(--spacing-3xl);
        display: flex;
        align-items: center;
        justify-content: center;
    }

    .form-container {
        width: 100%;
        max-width: 400px;
    }

    .form-header {
        text-align: center;
        margin-bottom: var(--spacing-2xl);
    }

    .form-title {
        font-size: var(--font-size-3xl);
        font-weight: 700;
        color: var(--neutral-black);
        margin-bottom: var(--spacing-sm);
    }

    .form-subtitle {
        color: var(--neutral-gray);
        font-size: var(--font-size-base);
        margin: 0;
    }

    .auth-form {
        width: 100%;
    }

    .form-group {
        margin-bottom: var(--spacing-xl);
    }

    .form-label {
        display: flex;
        align-items: center;
        font-weight: 600;
        color: var(--neutral-black);
        margin-bottom: var(--spacing-sm);
    }

    .form-control {
        height: 50px;
        border: 2px solid var(--light-gray);
        border-radius: var(--radius-md);
        padding: var(--spacing-sm) var(--spacing-md);
        font-size: var(--font-size-base);
        transition: all var(--transition-fast);
        background: var(--white);
    }

    .form-control:focus {
        border-color: var(--primary-blue);
        box-shadow: 0 0 0 3px rgba(25, 118, 210, 0.1);
        outline: none;
    }

    .form-control.is-invalid {
        border-color: var(--primary-red);
    }

    .password-input-wrapper {
        position: relative;
    }

    .password-toggle {
        position: absolute;
        right: var(--spacing-md);
        top: 50%;
        transform: translateY(-50%);
        background: none;
        border: none;
        color: var(--neutral-gray);
        cursor: pointer;
        padding: var(--spacing-xs);
        transition: color var(--transition-fast);
    }

    .password-toggle:hover {
        color: var(--primary-blue);
    }

    .form-options {
        display: flex;
        justify-content: space-between;
        align-items: center;
        margin-bottom: var(--spacing-xl);
        flex-wrap: wrap;
        gap: var(--spacing-sm);
    }

    .form-check {
        display: flex;
        align-items: center;
        gap: var(--spacing-sm);
    }

    .form-check-input {
        width: 18px;
        height: 18px;
        border: 2px solid var(--neutral-gray);
        border-radius: var(--radius-sm);
    }

    .form-check-input:checked {
        background-color: var(--primary-blue);
        border-color: var(--primary-blue);
    }

    .form-check-label {
        font-size: var(--font-size-sm);
        color: var(--neutral-gray);
        margin: 0;
    }

    .forgot-password-link {
        color: var(--primary-blue);
        text-decoration: none;
        font-size: var(--font-size-sm);
        font-weight: 500;
        transition: color var(--transition-fast);
    }

    .forgot-password-link:hover {
        color: var(--primary-red);
    }

    .btn-login {
        width: 100%;
        height: 50px;
        font-size: var(--font-size-lg);
        font-weight: 600;
        margin-bottom: var(--spacing-xl);
    }

    .auth-switch {
        text-align: center;
        padding: var(--spacing-lg) 0;
        border-top: 1px solid var(--light-gray);
        margin-bottom: var(--spacing-lg);
    }

    .switch-text {
        color: var(--neutral-gray);
        margin-bottom: var(--spacing-sm);
        font-size: var(--font-size-sm);
    }

    .switch-link {
        color: var(--primary-blue);
        text-decoration: none;
        font-weight: 600;
        font-size: var(--font-size-base);
        transition: color var(--transition-fast);
    }

    .switch-link:hover {
        color: var(--primary-red);
    }

    .back-to-home {
        text-align: center;
    }

    .home-link {
        color: var(--neutral-gray);
        text-decoration: none;
        font-size: var(--font-size-sm);
        transition: color var(--transition-fast);
    }

    .home-link:hover {
        color: var(--primary-blue);
    }

    /* Responsive Design */
    @media (max-width: 992px) {
        .auth-wrapper {
            grid-template-columns: 1fr;
        }

        .auth-branding {
            padding: var(--spacing-2xl);
            text-align: center;
        }

        .welcome-title {
            font-size: var(--font-size-2xl);
        }

        .branding-features {
            flex-direction: row;
            justify-content: center;
            flex-wrap: wrap;
        }
    }

    @media (max-width: 768px) {
        .auth-body-japanese {
            padding: var(--spacing-sm);
        }

        .auth-branding,
        .auth-form-section {
            padding: var(--spacing-xl);
        }

        .form-options {
            flex-direction: column;
            align-items: flex-start;
            gap: var(--spacing-md);
        }

        .branding-features {
            flex-direction: column;
            align-items: center;
        }
    }
    </style>

    <!-- Bootstrap JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.3/dist/js/bootstrap.bundle.min.js"></script>

    <!-- Custom JavaScript -->
    <script>
        // Toggle password visibility
        function togglePassword() {
            const passwordInput = document.getElementById('password');
            const toggleIcon = document.getElementById('passwordToggleIcon');

            if (passwordInput.type === 'password') {
                passwordInput.type = 'text';
                toggleIcon.classList.remove('fa-eye');
                toggleIcon.classList.add('fa-eye-slash');
            } else {
                passwordInput.type = 'password';
                toggleIcon.classList.remove('fa-eye-slash');
                toggleIcon.classList.add('fa-eye');
            }
        }

        // Form validation and submission
        document.getElementById('loginForm').addEventListener('submit', function(e) {
            const submitBtn = this.querySelector('.btn-login');
            const originalText = submitBtn.innerHTML;

            // Show loading state
            submitBtn.innerHTML = '<i class="fas fa-spinner fa-spin me-2"></i>Đang đăng nhập...';
            submitBtn.disabled = true;

            // Reset button after 3 seconds if form doesn't submit
            setTimeout(() => {
                if (submitBtn.disabled) {
                    submitBtn.innerHTML = originalText;
                    submitBtn.disabled = false;
                }
            }, 3000);
        });

        // Auto-hide alerts after 5 seconds
        document.addEventListener('DOMContentLoaded', function() {
            const alerts = document.querySelectorAll('.alert');
            alerts.forEach(alert => {
                setTimeout(() => {
                    const bsAlert = new bootstrap.Alert(alert);
                    bsAlert.close();
                }, 5000);
            });
        });

        // Add floating animation to form elements
        const formInputs = document.querySelectorAll('.form-control');
        formInputs.forEach(input => {
            input.addEventListener('focus', function() {
                this.parentElement.style.transform = 'translateY(-2px)';
                this.parentElement.style.transition = 'transform 0.3s ease';
            });

            input.addEventListener('blur', function() {
                this.parentElement.style.transform = 'translateY(0)';
            });
        });
    </script>
</body>
</html>
