@extends('user.dashboard_user')

@section('title', 'Giỏ Hàng - Shop Thời Trang Nhật Bản')
@section('description', 'Xem và quản lý giỏ hàng của bạn tại Shop Thời Trang. Thanh toán nhanh chóng và an toàn.')
@section('keywords', 'giỏ hàng, thanh toán, mua sắm online, thời trang nhật bản')

@section('content')
<main class="cart-main-japanese">
    <div class="container">
        <!-- Breadcrumb -->
        <nav aria-label="breadcrumb" class="cart-breadcrumb">
            <ol class="breadcrumb">
                <li class="breadcrumb-item">
                    <a href="{{ route('home.index') }}">
                        <i class="fas fa-home me-1"></i>
                        Trang chủ
                    </a>
                </li>
                <li class="breadcrumb-item active" aria-current="page">
                    <i class="fas fa-shopping-cart me-1"></i>
                    Giỏ hàng
                </li>
            </ol>
        </nav>

        <!-- Page Header -->
        <div class="cart-header">
            <h1 class="cart-title">
                <i class="fas fa-shopping-cart me-3"></i>
                Giỏ Hàng Của Bạn
            </h1>
            <p class="cart-subtitle">Xem lại các sản phẩm đã chọn và tiến hành thanh toán</p>
        </div>

        @if(empty($product) || count(array_filter($product)) == 0)
            <!-- Empty Cart -->
            <div class="empty-cart-japanese">
                <div class="empty-cart-content">
                    <div class="empty-cart-icon">
                        <i class="fas fa-shopping-cart"></i>
                    </div>
                    <h3 class="empty-cart-title">Giỏ hàng trống</h3>
                    <p class="empty-cart-text">Bạn chưa có sản phẩm nào trong giỏ hàng. Hãy khám phá bộ sưu tập của chúng tôi!</p>
                    <a href="{{ route('home.index') }}" class="btn-japanese btn-primary">
                        <i class="fas fa-shopping-bag me-2"></i>
                        Tiếp tục mua sắm
                    </a>
                </div>
            </div>
        @else
            <!-- Cart Content -->
            <div class="cart-content-japanese">
                <div class="row">
                    <!-- Cart Items -->
                    <div class="col-lg-8">
                        <div class="cart-items-section">
                            <div class="cart-items-header">
                                <h3 class="section-title">
                                    <i class="fas fa-list me-2"></i>
                                    Sản phẩm ({{ count(array_filter($product)) }} món)
                                </h3>
                            </div>

                            <form action="{{ route('order.addOrder') }}" method="get" class="cart-form-japanese" id="cartForm">
                                @csrf
                                <div class="cart-items-list">
                                    @foreach($product as $item)
                                        @if(!empty($item))
                                        <div class="cart-item-japanese" data-product-id="{{ $item->id_product }}">
                                            <div class="cart-item-content">
                                                <div class="row align-items-center">
                                                    <!-- Product Image -->
                                                    <div class="col-md-3">
                                                        <div class="cart-item-image">
                                                            <img src="{{ asset('uploads/productimage/' . $item->image_address_product) }}"
                                                                 alt="{{ $item->name_product }}"
                                                                 class="product-image">
                                                        </div>
                                                    </div>

                                                    <!-- Product Info -->
                                                    <div class="col-md-5">
                                                        <div class="cart-item-info">
                                                            <h4 class="product-name">
                                                                <a href="{{ route('product.indexDetailproduct', ['id' => $item->id_product]) }}">
                                                                    {{ $item->name_product }}
                                                                </a>
                                                            </h4>
                                                            <div class="product-details">
                                                                <span class="product-price">
                                                                    <i class="fas fa-tag me-1"></i>
                                                                    {{ number_format($item->price_product, 0, ',', '.') }}đ
                                                                </span>
                                                                <span class="product-quantity">
                                                                    <i class="fas fa-cube me-1"></i>
                                                                    Số lượng: {{ $item->quantity_product }}
                                                                </span>
                                                            </div>
                                                            <div class="product-total">
                                                                <strong>
                                                                    Thành tiền:
                                                                    <span class="total-price">
                                                                        {{ number_format($item->price_product * $item->quantity_product, 0, ',', '.') }}đ
                                                                    </span>
                                                                </strong>
                                                            </div>
                                                        </div>
                                                    </div>

                                                    <!-- Quantity Controls -->
                                                    <div class="col-md-2">
                                                        <div class="quantity-controls">
                                                            <label class="quantity-label">Số lượng</label>
                                                            <div class="quantity-input-group">
                                                                <button type="button" class="quantity-btn minus-btn"
                                                                        onclick="updateQuantity({{ $item->id_product }}, -1)">
                                                                    <i class="fas fa-minus"></i>
                                                                </button>
                                                                <input type="number"
                                                                       class="quantity-input"
                                                                       value="{{ $item->quantity_product }}"
                                                                       min="1"
                                                                       max="99"
                                                                       id="quantity-{{ $item->id_product }}"
                                                                       onchange="updateQuantityDirect({{ $item->id_product }}, this.value)">
                                                                <button type="button" class="quantity-btn plus-btn"
                                                                        onclick="updateQuantity({{ $item->id_product }}, 1)">
                                                                    <i class="fas fa-plus"></i>
                                                                </button>
                                                            </div>
                                                        </div>
                                                    </div>

                                                    <!-- Remove Button -->
                                                    <div class="col-md-2">
                                                        <div class="cart-item-actions">
                                                            <button type="button"
                                                                    class="btn-remove-item"
                                                                    onclick="removeCartItem({{ $item->id_product }})"
                                                                    title="Xóa sản phẩm">
                                                                <i class="fas fa-trash-alt"></i>
                                                                <span>Xóa</span>
                                                            </button>
                                                        </div>
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                        @endif
                                    @endforeach
                                </div>
                            </form>
                        </div>
                    </div>

                    <!-- Cart Summary -->
                    <div class="col-lg-4">
                        <div class="cart-summary-japanese">
                            <div class="summary-header">
                                <h3 class="summary-title">
                                    <i class="fas fa-calculator me-2"></i>
                                    Tổng Kết Đơn Hàng
                                </h3>
                            </div>

                            <div class="summary-content">
                                <!-- Subtotal -->
                                <div class="summary-row">
                                    <span class="summary-label">Tạm tính:</span>
                                    <span class="summary-value" id="subtotal">{{ number_format($totalAll, 0, ',', '.') }}đ</span>
                                </div>

                                <!-- Shipping -->
                                <div class="summary-row">
                                    <span class="summary-label">Phí vận chuyển:</span>
                                    <span class="summary-value shipping-fee">Miễn phí</span>
                                </div>

                                <!-- Discount -->
                                <div class="summary-row">
                                    <span class="summary-label">Giảm giá:</span>
                                    <span class="summary-value discount">0đ</span>
                                </div>

                                <div class="summary-divider"></div>

                                <!-- Total -->
                                <div class="summary-row summary-total">
                                    <span class="summary-label">Tổng cộng:</span>
                                    <span class="summary-value total-amount" id="totalAmount">{{ number_format($totalAll, 0, ',', '.') }}đ</span>
                                </div>

                                <!-- Coupon Code -->
                                <div class="coupon-section">
                                    <h4 class="coupon-title">Mã giảm giá</h4>
                                    <div class="coupon-input-group">
                                        <input type="text" class="coupon-input" placeholder="Nhập mã giảm giá" id="couponCode">
                                        <button type="button" class="btn-apply-coupon">
                                            <i class="fas fa-check"></i>
                                        </button>
                                    </div>
                                </div>

                                <!-- Action Buttons -->
                                <div class="cart-actions">
                                    <button type="submit" form="cartForm" class="btn-japanese btn-primary btn-checkout">
                                        <i class="fas fa-credit-card me-2"></i>
                                        Tiến Hành Thanh Toán
                                    </button>

                                    <a href="{{ route('home.index') }}" class="btn-japanese btn-outline btn-continue-shopping">
                                        <i class="fas fa-arrow-left me-2"></i>
                                        Tiếp Tục Mua Sắm
                                    </a>
                                </div>

                                <!-- Security Info -->
                                <div class="security-info">
                                    <div class="security-item">
                                        <i class="fas fa-shield-alt"></i>
                                        <span>Thanh toán an toàn 100%</span>
                                    </div>
                                    <div class="security-item">
                                        <i class="fas fa-truck"></i>
                                        <span>Giao hàng miễn phí toàn quốc</span>
                                    </div>
                                    <div class="security-item">
                                        <i class="fas fa-undo-alt"></i>
                                        <span>Đổi trả trong 30 ngày</span>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        @endif
    </div>
</main>

<style>
/* ===== JAPANESE CART STYLES ===== */

/* Main Cart Layout */
.cart-main-japanese {
    background: var(--light-gray);
    min-height: 100vh;
    padding: var(--spacing-2xl) 0;
}

/* Breadcrumb */
.cart-breadcrumb {
    margin-bottom: var(--spacing-xl);
}

.breadcrumb {
    background: var(--white);
    border-radius: var(--radius-md);
    padding: var(--spacing-md) var(--spacing-lg);
    box-shadow: var(--shadow-sm);
    margin: 0;
}

.breadcrumb-item a {
    color: var(--primary-blue);
    text-decoration: none;
    transition: color var(--transition-fast);
}

.breadcrumb-item a:hover {
    color: var(--primary-red);
}

.breadcrumb-item.active {
    color: var(--neutral-gray);
}

/* Cart Header */
.cart-header {
    text-align: center;
    margin-bottom: var(--spacing-3xl);
    background: var(--white);
    padding: var(--spacing-2xl);
    border-radius: var(--radius-lg);
    box-shadow: var(--shadow-sm);
}

.cart-title {
    font-size: var(--font-size-3xl);
    font-weight: 700;
    color: var(--neutral-black);
    margin-bottom: var(--spacing-md);
    display: flex;
    align-items: center;
    justify-content: center;
}

.cart-subtitle {
    color: var(--neutral-gray);
    font-size: var(--font-size-lg);
    margin: 0;
}

/* Empty Cart */
.empty-cart-japanese {
    background: var(--white);
    border-radius: var(--radius-lg);
    padding: var(--spacing-3xl);
    text-align: center;
    box-shadow: var(--shadow-sm);
}

.empty-cart-icon {
    font-size: 80px;
    color: var(--neutral-gray);
    margin-bottom: var(--spacing-xl);
    opacity: 0.5;
}

.empty-cart-title {
    font-size: var(--font-size-2xl);
    font-weight: 600;
    color: var(--neutral-black);
    margin-bottom: var(--spacing-md);
}

.empty-cart-text {
    color: var(--neutral-gray);
    font-size: var(--font-size-lg);
    margin-bottom: var(--spacing-2xl);
    max-width: 400px;
    margin-left: auto;
    margin-right: auto;
}

/* Cart Content */
.cart-content-japanese {
    background: var(--white);
    border-radius: var(--radius-lg);
    padding: var(--spacing-2xl);
    box-shadow: var(--shadow-sm);
}

/* Cart Items Section */
.cart-items-section {
    margin-bottom: var(--spacing-xl);
}

.cart-items-header {
    margin-bottom: var(--spacing-xl);
    padding-bottom: var(--spacing-lg);
    border-bottom: 2px solid var(--light-gray);
}

.section-title {
    font-size: var(--font-size-xl);
    font-weight: 600;
    color: var(--neutral-black);
    margin: 0;
    display: flex;
    align-items: center;
}

/* Cart Item */
.cart-item-japanese {
    border: 1px solid var(--light-gray);
    border-radius: var(--radius-lg);
    margin-bottom: var(--spacing-lg);
    transition: all var(--transition-normal);
    background: var(--white);
}

.cart-item-japanese:hover {
    box-shadow: var(--shadow-md);
    transform: translateY(-2px);
}

.cart-item-content {
    padding: var(--spacing-lg);
}

.cart-item-image {
    text-align: center;
}

.product-image {
    width: 100%;
    max-width: 150px;
    height: 150px;
    object-fit: contain;
    border-radius: var(--radius-md);
    background: var(--light-gray);
    padding: var(--spacing-sm);
}

.cart-item-info {
    padding: var(--spacing-md) 0;
}

.product-name {
    margin-bottom: var(--spacing-md);
}

.product-name a {
    color: var(--neutral-black);
    text-decoration: none;
    font-size: var(--font-size-lg);
    font-weight: 600;
    transition: color var(--transition-fast);
    line-height: 1.4;
}

.product-name a:hover {
    color: var(--primary-red);
}

.product-details {
    display: flex;
    flex-direction: column;
    gap: var(--spacing-sm);
    margin-bottom: var(--spacing-md);
}

.product-price,
.product-quantity {
    color: var(--neutral-gray);
    font-size: var(--font-size-base);
    display: flex;
    align-items: center;
}

.product-total {
    font-size: var(--font-size-lg);
    color: var(--primary-red);
}

.total-price {
    font-weight: 700;
}

/* Quantity Controls */
.quantity-controls {
    text-align: center;
}

.quantity-label {
    display: block;
    font-size: var(--font-size-sm);
    font-weight: 600;
    color: var(--neutral-black);
    margin-bottom: var(--spacing-sm);
}

.quantity-input-group {
    display: flex;
    align-items: center;
    justify-content: center;
    border: 2px solid var(--light-gray);
    border-radius: var(--radius-md);
    overflow: hidden;
    background: var(--white);
}

.quantity-btn {
    background: var(--light-gray);
    border: none;
    width: 35px;
    height: 35px;
    display: flex;
    align-items: center;
    justify-content: center;
    cursor: pointer;
    transition: all var(--transition-fast);
    color: var(--neutral-black);
}

.quantity-btn:hover {
    background: var(--primary-blue);
    color: var(--white);
}

.quantity-input {
    border: none;
    width: 50px;
    height: 35px;
    text-align: center;
    font-weight: 600;
    background: var(--white);
    outline: none;
}

/* Remove Button */
.cart-item-actions {
    text-align: center;
}

.btn-remove-item {
    background: var(--white);
    border: 2px solid var(--primary-red);
    color: var(--primary-red);
    padding: var(--spacing-sm) var(--spacing-md);
    border-radius: var(--radius-md);
    cursor: pointer;
    transition: all var(--transition-fast);
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: var(--spacing-xs);
    font-size: var(--font-size-sm);
    font-weight: 500;
    width: 100%;
}

.btn-remove-item:hover {
    background: var(--primary-red);
    color: var(--white);
    transform: translateY(-2px);
}

/* Cart Summary */
.cart-summary-japanese {
    background: var(--white);
    border: 2px solid var(--light-gray);
    border-radius: var(--radius-lg);
    position: sticky;
    top: 100px;
}

.summary-header {
    background: var(--light-gray);
    padding: var(--spacing-lg);
    border-radius: var(--radius-lg) var(--radius-lg) 0 0;
}

.summary-title {
    font-size: var(--font-size-xl);
    font-weight: 600;
    color: var(--neutral-black);
    margin: 0;
    display: flex;
    align-items: center;
}

.summary-content {
    padding: var(--spacing-lg);
}

.summary-row {
    display: flex;
    justify-content: space-between;
    align-items: center;
    margin-bottom: var(--spacing-md);
    font-size: var(--font-size-base);
}

.summary-label {
    color: var(--neutral-gray);
    font-weight: 500;
}

.summary-value {
    color: var(--neutral-black);
    font-weight: 600;
}

.summary-divider {
    height: 1px;
    background: var(--light-gray);
    margin: var(--spacing-lg) 0;
}

.summary-total {
    font-size: var(--font-size-lg);
    font-weight: 700;
    color: var(--primary-red);
    padding: var(--spacing-md);
    background: rgba(211, 47, 47, 0.05);
    border-radius: var(--radius-md);
    margin-bottom: var(--spacing-xl);
}

/* Coupon Section */
.coupon-section {
    margin-bottom: var(--spacing-xl);
    padding: var(--spacing-lg);
    background: var(--light-gray);
    border-radius: var(--radius-md);
}

.coupon-title {
    font-size: var(--font-size-base);
    font-weight: 600;
    color: var(--neutral-black);
    margin-bottom: var(--spacing-md);
}

.coupon-input-group {
    display: flex;
    gap: var(--spacing-sm);
}

.coupon-input {
    flex: 1;
    padding: var(--spacing-sm) var(--spacing-md);
    border: 2px solid var(--light-gray);
    border-radius: var(--radius-md);
    font-size: var(--font-size-sm);
    outline: none;
    transition: border-color var(--transition-fast);
}

.coupon-input:focus {
    border-color: var(--primary-blue);
}

.btn-apply-coupon {
    background: var(--primary-blue);
    color: var(--white);
    border: none;
    padding: var(--spacing-sm) var(--spacing-md);
    border-radius: var(--radius-md);
    cursor: pointer;
    transition: all var(--transition-fast);
    min-width: 50px;
}

.btn-apply-coupon:hover {
    background: var(--primary-red);
    transform: scale(1.05);
}

/* Cart Actions */
.cart-actions {
    display: flex;
    flex-direction: column;
    gap: var(--spacing-md);
    margin-bottom: var(--spacing-xl);
}

.btn-checkout,
.btn-continue-shopping {
    width: 100%;
    padding: var(--spacing-md);
    font-size: var(--font-size-base);
    font-weight: 600;
    text-align: center;
    text-decoration: none;
    border: none;
    cursor: pointer;
}

/* Security Info */
.security-info {
    display: flex;
    flex-direction: column;
    gap: var(--spacing-sm);
}

.security-item {
    display: flex;
    align-items: center;
    gap: var(--spacing-sm);
    font-size: var(--font-size-sm);
    color: var(--neutral-gray);
}

.security-item i {
    color: var(--success-green);
    width: 16px;
}

/* Responsive Design */
@media (max-width: 992px) {
    .cart-summary-japanese {
        position: static;
        margin-top: var(--spacing-xl);
    }

    .cart-content-japanese {
        padding: var(--spacing-lg);
    }
}

@media (max-width: 768px) {
    .cart-main-japanese {
        padding: var(--spacing-lg) 0;
    }

    .cart-header {
        padding: var(--spacing-lg);
    }

    .cart-title {
        font-size: var(--font-size-2xl);
        flex-direction: column;
        gap: var(--spacing-sm);
    }

    .cart-item-content {
        padding: var(--spacing-md);
    }

    .product-details {
        margin-bottom: var(--spacing-lg);
    }

    .quantity-controls,
    .cart-item-actions {
        margin-top: var(--spacing-md);
    }

    .summary-content {
        padding: var(--spacing-md);
    }
}

@media (max-width: 576px) {
    .cart-content-japanese {
        padding: var(--spacing-md);
    }

    .cart-item-japanese .row > div {
        margin-bottom: var(--spacing-md);
    }

    .product-image {
        max-width: 120px;
        height: 120px;
    }

    .quantity-input-group {
        max-width: 120px;
        margin: 0 auto;
    }
}
</style>

<script>
// Cart functionality
document.addEventListener('DOMContentLoaded', function() {
    // Update quantity functions
    window.updateQuantity = function(productId, change) {
        const quantityInput = document.getElementById(`quantity-${productId}`);
        let currentQuantity = parseInt(quantityInput.value);
        let newQuantity = currentQuantity + change;

        if (newQuantity < 1) newQuantity = 1;
        if (newQuantity > 99) newQuantity = 99;

        quantityInput.value = newQuantity;
        updateCartItem(productId, newQuantity);
    };

    window.updateQuantityDirect = function(productId, quantity) {
        let newQuantity = parseInt(quantity);
        if (newQuantity < 1) newQuantity = 1;
        if (newQuantity > 99) newQuantity = 99;

        document.getElementById(`quantity-${productId}`).value = newQuantity;
        updateCartItem(productId, newQuantity);
    };

    // Remove cart item
    window.removeCartItem = function(productId) {
        if (confirm('Bạn có chắc chắn muốn xóa sản phẩm này khỏi giỏ hàng?')) {
            // Show loading state
            const cartItem = document.querySelector(`[data-product-id="${productId}"]`);
            cartItem.style.opacity = '0.5';
            cartItem.style.pointerEvents = 'none';

            // Redirect to delete route
            window.location.href = `/cart/delete/${productId}`;
        }
    };

    // Update cart item via AJAX (if you want to implement AJAX updates)
    function updateCartItem(productId, quantity) {
        // For now, we'll just update the display
        // You can implement AJAX call here to update the cart on server
        updateCartDisplay();
    }

    // Update cart display
    function updateCartDisplay() {
        let total = 0;
        const cartItems = document.querySelectorAll('.cart-item-japanese');

        cartItems.forEach(item => {
            const productId = item.dataset.productId;
            const quantityInput = document.getElementById(`quantity-${productId}`);
            const priceElement = item.querySelector('.product-price');
            const totalElement = item.querySelector('.total-price');

            if (quantityInput && priceElement && totalElement) {
                const quantity = parseInt(quantityInput.value);
                const price = parseFloat(priceElement.textContent.replace(/[^\d]/g, ''));
                const itemTotal = price * quantity;

                totalElement.textContent = formatCurrency(itemTotal);
                total += itemTotal;
            }
        });

        // Update summary
        document.getElementById('subtotal').textContent = formatCurrency(total);
        document.getElementById('totalAmount').textContent = formatCurrency(total);
    }

    // Format currency
    function formatCurrency(amount) {
        return new Intl.NumberFormat('vi-VN', {
            style: 'currency',
            currency: 'VND',
            minimumFractionDigits: 0
        }).format(amount).replace('₫', 'đ');
    }

    // Apply coupon
    window.applyCoupon = function() {
        const couponCode = document.getElementById('couponCode').value.trim();
        const applyBtn = document.querySelector('.btn-apply-coupon');

        if (!couponCode) {
            alert('Vui lòng nhập mã giảm giá');
            return;
        }

        // Show loading state
        const originalContent = applyBtn.innerHTML;
        applyBtn.innerHTML = '<i class="fas fa-spinner fa-spin"></i>';
        applyBtn.disabled = true;

        // Simulate API call (replace with actual implementation)
        setTimeout(() => {
            applyBtn.innerHTML = originalContent;
            applyBtn.disabled = false;
            alert('Mã giảm giá không hợp lệ hoặc đã hết hạn');
        }, 1000);
    };

    // Form submission handling
    const cartForm = document.getElementById('cartForm');
    if (cartForm) {
        cartForm.addEventListener('submit', function(e) {
            const checkoutBtn = document.querySelector('.btn-checkout');
            const originalText = checkoutBtn.innerHTML;

            // Show loading state
            checkoutBtn.innerHTML = '<i class="fas fa-spinner fa-spin me-2"></i>Đang xử lý...';
            checkoutBtn.disabled = true;

            // Reset button after 5 seconds if form doesn't submit
            setTimeout(() => {
                if (checkoutBtn.disabled) {
                    checkoutBtn.innerHTML = originalText;
                    checkoutBtn.disabled = false;
                }
            }, 5000);
        });
    }

    // Smooth animations
    const cartItems = document.querySelectorAll('.cart-item-japanese');
    cartItems.forEach((item, index) => {
        item.style.opacity = '0';
        item.style.transform = 'translateY(20px)';

        setTimeout(() => {
            item.style.transition = 'opacity 0.5s ease, transform 0.5s ease';
            item.style.opacity = '1';
            item.style.transform = 'translateY(0)';
        }, index * 100);
    });
});
</script>
@endsection