@extends('user.dashboard_user')

@section('title', 'Trang Chủ - Shop Thời Trang Nhật Bản')
@section('description', 'Khám phá bộ sưu tập thời trang cao cấp với phong cách Nhật <PERSON>. Sản phẩm chất <PERSON>, thiế<PERSON> kế tinh tế, giao hàng toàn quốc.')
@section('keywords', 'thời trang nhật bản, quầ<PERSON> <PERSON><PERSON> cao cấ<PERSON>, phụ kiện thời trang, mua sắm online')

@section('content')

@if(session('error'))
    <div class="alert alert-danger alert-dismissible fade show" role="alert">
        {{ session('error') }}
        <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
    </div>
@endif

<main class="main-content-japanese">
    <!-- HERO SECTION -->
    <section class="hero-section-japanese">
        <div class="container">
            <div class="hero-content">
                <div class="row align-items-center">
                    <div class="col-lg-6">
                        <div class="hero-text">
                            <h1 class="hero-title">
                                <span class="hero-subtitle">Phong Cách</span>
                                <span class="hero-main-title">Nhật Bản</span>
                                <span class="hero-description">Tinh Tế & Thanh Lịch</span>
                            </h1>
                            <p class="hero-paragraph">
                                Khám phá bộ sưu tập thời trang cao cấp với thiết kế minimalist,
                                chất lượng vượt trội và phong cách đặc trưng của xứ sở hoa anh đào.
                            </p>
                            <div class="hero-actions">
                                <a href="#new-products" class="btn-japanese btn-primary">
                                    <i class="fas fa-shopping-bag"></i>
                                    Khám Phá Ngay
                                </a>
                                <a href="{{ route('post.indexListPostUser') }}" class="btn-japanese btn-outline">
                                    <i class="fas fa-newspaper"></i>
                                    Tin Tức
                                </a>
                            </div>
                        </div>
                    </div>
                    <div class="col-lg-6">
                        <div class="hero-image">
                            <div class="hero-image-wrapper">
                                <div class="hero-placeholder">
                                    <i class="fas fa-tshirt hero-icon"></i>
                                    <p>Thời Trang Nhật Bản</p>
                                </div>
                                <div class="hero-decoration"></div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </section>
    <!-- /HERO SECTION -->

    <!-- FEATURES SECTION -->
    <section class="features-section-japanese">
        <div class="container">
            <div class="row">
                <div class="col-lg-3 col-md-6 mb-4">
                    <div class="feature-card">
                        <div class="feature-icon">
                            <i class="fas fa-shipping-fast"></i>
                        </div>
                        <h4 class="feature-title">Giao Hàng Nhanh</h4>
                        <p class="feature-description">Giao hàng toàn quốc trong 24-48h</p>
                    </div>
                </div>
                <div class="col-lg-3 col-md-6 mb-4">
                    <div class="feature-card">
                        <div class="feature-icon">
                            <i class="fas fa-medal"></i>
                        </div>
                        <h4 class="feature-title">Chất Lượng Cao</h4>
                        <p class="feature-description">Sản phẩm chính hãng, chất lượng đảm bảo</p>
                    </div>
                </div>
                <div class="col-lg-3 col-md-6 mb-4">
                    <div class="feature-card">
                        <div class="feature-icon">
                            <i class="fas fa-undo-alt"></i>
                        </div>
                        <h4 class="feature-title">Đổi Trả Dễ Dàng</h4>
                        <p class="feature-description">Đổi trả trong 30 ngày nếu không hài lòng</p>
                    </div>
                </div>
                <div class="col-lg-3 col-md-6 mb-4">
                    <div class="feature-card">
                        <div class="feature-icon">
                            <i class="fas fa-headset"></i>
                        </div>
                        <h4 class="feature-title">Hỗ Trợ 24/7</h4>
                        <p class="feature-description">Tư vấn và hỗ trợ khách hàng mọi lúc</p>
                    </div>
                </div>
            </div>
        </div>
    </section>
    <!-- /FEATURES SECTION -->

    <!-- NEW PRODUCTS SECTION -->
    <section id="new-products" class="new-products-section-japanese">
        <div class="container">
            <div class="section-header">
                <h2 class="section-title-japanese">Sản Phẩm Mới</h2>
                <p class="section-subtitle">Những sản phẩm mới nhất với thiết kế độc đáo</p>
                <div class="divider-japanese"></div>
            </div>

            <div class="products-grid">
                <div class="newproduct-slider">
                    @foreach($get6newproducts as $get6newproduct)
                    <div class="product-slide">
                        <div class="product-card-japanese">
                            <div class="product-image-wrapper">
                                <img src="{{ asset('uploads/productimage/' . $get6newproduct->image_address_product) }}"
                                     alt="{{ $get6newproduct->name_product }}"
                                     class="product-image">
                                <div class="product-overlay">
                                    <a href="{{ route('product.indexDetailproduct', ['id' => $get6newproduct->id_product]) }}"
                                       class="product-view-btn">
                                        <i class="fas fa-eye"></i>
                                    </a>
                                </div>
                                <div class="product-badge">Mới</div>
                            </div>
                            <div class="product-info">
                                <div class="product-category">
                                    @foreach($productsWithCategorys as $productsWithCategory)
                                        @if($get6newproduct->id_category == $productsWithCategory->id_category)
                                            {{ $productsWithCategory->name_category }}
                                            @break
                                        @endif
                                    @endforeach
                                </div>
                                <h4 class="product-name">{{ $get6newproduct->name_product }}</h4>
                                <div class="product-manufacturer">
                                    @foreach($productsWithManufacturers as $productsWithManufacturer)
                                        @if($get6newproduct->id_manufacturer == $productsWithManufacturer->id_manufacturer)
                                            {{ $productsWithManufacturer->name_manufacturer }}
                                            @break
                                        @endif
                                    @endforeach
                                </div>
                                <div class="product-price">
                                    <span class="current-price">{{ number_format($get6newproduct->price_product, 0, ',', '.') }}đ</span>
                                </div>
                                <div class="product-actions">
                                    <form action="{{ route('cart.addCart') }}" method="POST" class="add-to-cart-form">
                                        @csrf
                                        <input type="hidden" name="id_product" value="{{ $get6newproduct->id_product }}">
                                        <input type="hidden" name="quantity" value="1">
                                        <button type="submit" class="btn-japanese btn-primary btn-add-cart">
                                            <i class="fas fa-shopping-cart"></i>
                                            Thêm vào giỏ
                                        </button>
                                    </form>
                                </div>
                            </div>
                        </div>
                    </div>
                    @endforeach
                </div>
            </div>
        </div>
    </section>
    <!-- /NEW PRODUCTS SECTION -->

    <!-- ALL PRODUCTS SECTION -->
    <section class="all-products-section-japanese">
        <div class="container">
            <div class="section-header">
                <h2 class="section-title-japanese">Tất Cả Sản Phẩm</h2>
                <p class="section-subtitle">Bộ sưu tập đầy đủ với đa dạng lựa chọn</p>
                <div class="divider-japanese"></div>
            </div>

            <div class="products-grid">
                <div class="row">
                    @foreach($products as $product)
                    <div class="col-lg-3 col-md-4 col-sm-6 mb-4">
                        <div class="product-card-japanese">
                            <div class="product-image-wrapper">
                                <img src="{{ asset('uploads/productimage/' . $product->image_address_product) }}"
                                     alt="{{ $product->name_product }}"
                                     class="product-image">
                                <div class="product-overlay">
                                    <a href="{{ route('product.indexDetailproduct', ['id' => $product->id_product]) }}"
                                       class="product-view-btn">
                                        <i class="fas fa-eye"></i>
                                    </a>
                                </div>
                            </div>
                            <div class="product-info">
                                <div class="product-category">
                                    @foreach($productsWithCategorys as $productsWithCategory)
                                        @if($product->id_category == $productsWithCategory->id_category)
                                            {{ $productsWithCategory->name_category }}
                                            @break
                                        @endif
                                    @endforeach
                                </div>
                                <h4 class="product-name">{{ $product->name_product }}</h4>
                                <div class="product-manufacturer">
                                    @foreach($productsWithManufacturers as $productsWithManufacturer)
                                        @if($product->id_manufacturer == $productsWithManufacturer->id_manufacturer)
                                            {{ $productsWithManufacturer->name_manufacturer }}
                                            @break
                                        @endif
                                    @endforeach
                                </div>
                                <div class="product-price">
                                    <span class="current-price">{{ number_format($product->price_product, 0, ',', '.') }}đ</span>
                                </div>
                                <div class="product-actions">
                                    <form action="{{ route('cart.addCart') }}" method="POST" class="add-to-cart-form">
                                        @csrf
                                        <input type="hidden" name="id_product" value="{{ $product->id_product }}">
                                        <input type="hidden" name="quantity" value="1">
                                        <button type="submit" class="btn-japanese btn-primary btn-add-cart">
                                            <i class="fas fa-shopping-cart"></i>
                                            Thêm vào giỏ
                                        </button>
                                    </form>
                                </div>
                            </div>
                        </div>
                    </div>
                    @endforeach
                </div>

                <!-- Pagination -->
                <div class="pagination-wrapper-japanese">
                    {{ $products->links() }}
                </div>
            </div>
        </div>
    </section>
    <!-- /ALL PRODUCTS SECTION -->
</main>

<style>
/* ===== JAPANESE HOME PAGE STYLES ===== */

/* Main Content */
.main-content-japanese {
    background: var(--white);
}

/* Hero Section */
.hero-section-japanese {
    background: linear-gradient(135deg, var(--light-gray) 0%, var(--white) 100%);
    padding: var(--spacing-3xl) 0;
    min-height: 70vh;
    display: flex;
    align-items: center;
}

.hero-content {
    width: 100%;
}

.hero-text {
    padding-right: var(--spacing-xl);
}

.hero-title {
    margin-bottom: var(--spacing-xl);
}

.hero-subtitle {
    display: block;
    font-size: var(--font-size-lg);
    color: var(--neutral-gray);
    font-weight: 400;
    margin-bottom: var(--spacing-sm);
}

.hero-main-title {
    display: block;
    font-size: var(--font-size-4xl);
    color: var(--primary-red);
    font-weight: 700;
    margin-bottom: var(--spacing-sm);
    text-shadow: 2px 2px 4px rgba(0, 0, 0, 0.1);
}

.hero-description {
    display: block;
    font-size: var(--font-size-xl);
    color: var(--neutral-black);
    font-weight: 300;
    font-style: italic;
}

.hero-paragraph {
    font-size: var(--font-size-lg);
    line-height: 1.8;
    color: var(--neutral-gray);
    margin-bottom: var(--spacing-xl);
    max-width: 500px;
}

.hero-actions {
    display: flex;
    gap: var(--spacing-lg);
    flex-wrap: wrap;
}

.hero-image {
    display: flex;
    justify-content: center;
    align-items: center;
}

.hero-image-wrapper {
    position: relative;
    width: 400px;
    height: 400px;
    display: flex;
    align-items: center;
    justify-content: center;
}

.hero-placeholder {
    background: var(--white);
    border-radius: var(--radius-xl);
    box-shadow: var(--shadow-xl);
    padding: var(--spacing-3xl);
    text-align: center;
    width: 100%;
    height: 100%;
    display: flex;
    flex-direction: column;
    align-items: center;
    justify-content: center;
    border: 2px solid var(--light-gray);
}

.hero-icon {
    font-size: 80px;
    color: var(--primary-red);
    margin-bottom: var(--spacing-lg);
}

.hero-placeholder p {
    font-size: var(--font-size-xl);
    font-weight: 600;
    color: var(--neutral-black);
    margin: 0;
}

.hero-decoration {
    position: absolute;
    top: -20px;
    right: -20px;
    width: 100px;
    height: 100px;
    background: linear-gradient(45deg, var(--primary-red), var(--accent-gold));
    border-radius: 50%;
    opacity: 0.1;
    z-index: -1;
}

/* Features Section */
.features-section-japanese {
    padding: var(--spacing-3xl) 0;
    background: var(--white);
}

.feature-card {
    text-align: center;
    padding: var(--spacing-xl);
    border-radius: var(--radius-lg);
    transition: all var(--transition-normal);
    height: 100%;
    border: 1px solid var(--light-gray);
}

.feature-card:hover {
    transform: translateY(-5px);
    box-shadow: var(--shadow-lg);
    border-color: var(--primary-red);
}

.feature-icon {
    width: 80px;
    height: 80px;
    background: linear-gradient(135deg, var(--primary-red), var(--accent-gold));
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    margin: 0 auto var(--spacing-lg);
    transition: all var(--transition-normal);
}

.feature-card:hover .feature-icon {
    transform: scale(1.1);
}

.feature-icon i {
    font-size: var(--font-size-2xl);
    color: var(--white);
}

.feature-title {
    font-size: var(--font-size-xl);
    font-weight: 600;
    color: var(--neutral-black);
    margin-bottom: var(--spacing-md);
}

.feature-description {
    color: var(--neutral-gray);
    font-size: var(--font-size-base);
    line-height: 1.6;
    margin: 0;
}

/* Products Sections */
.new-products-section-japanese,
.all-products-section-japanese {
    padding: var(--spacing-3xl) 0;
    background: var(--light-gray);
}

.section-header {
    text-align: center;
    margin-bottom: var(--spacing-3xl);
}

.section-subtitle {
    font-size: var(--font-size-lg);
    color: var(--neutral-gray);
    margin-bottom: var(--spacing-lg);
    font-weight: 300;
}

/* Product Cards */
.product-card-japanese {
    background: var(--white);
    border-radius: var(--radius-lg);
    overflow: hidden;
    transition: all var(--transition-normal);
    height: 100%;
    box-shadow: var(--shadow-sm);
    border: 1px solid rgba(0, 0, 0, 0.05);
}

.product-card-japanese:hover {
    transform: translateY(-8px);
    box-shadow: var(--shadow-xl);
}

.product-image-wrapper {
    position: relative;
    height: 280px;
    overflow: hidden;
    background: var(--white);
    display: flex;
    align-items: center;
    justify-content: center;
    padding: var(--spacing-lg);
}

.product-image {
    width: 100%;
    height: 100%;
    object-fit: contain;
    transition: transform var(--transition-slow);
}

.product-card-japanese:hover .product-image {
    transform: scale(1.05);
}

.product-overlay {
    position: absolute;
    top: 0;
    left: 0;
    right: 0;
    bottom: 0;
    background: rgba(0, 0, 0, 0.7);
    display: flex;
    align-items: center;
    justify-content: center;
    opacity: 0;
    transition: all var(--transition-normal);
}

.product-card-japanese:hover .product-overlay {
    opacity: 1;
}

.product-view-btn {
    background: var(--white);
    color: var(--primary-red);
    width: 50px;
    height: 50px;
    border-radius: 50%;
    display: flex;
    align-items: center;
    justify-content: center;
    text-decoration: none;
    transition: all var(--transition-fast);
    transform: scale(0.8);
}

.product-card-japanese:hover .product-view-btn {
    transform: scale(1);
}

.product-view-btn:hover {
    background: var(--primary-red);
    color: var(--white);
    transform: scale(1.1);
}

.product-badge {
    position: absolute;
    top: var(--spacing-md);
    left: var(--spacing-md);
    background: var(--primary-red);
    color: var(--white);
    padding: var(--spacing-xs) var(--spacing-sm);
    border-radius: var(--radius-sm);
    font-size: var(--font-size-xs);
    font-weight: 600;
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

.product-info {
    padding: var(--spacing-lg);
    text-align: center;
}

.product-category {
    font-size: var(--font-size-xs);
    color: var(--neutral-gray);
    text-transform: uppercase;
    letter-spacing: 1px;
    margin-bottom: var(--spacing-sm);
    font-weight: 500;
}

.product-name {
    font-size: var(--font-size-lg);
    font-weight: 600;
    color: var(--neutral-black);
    margin-bottom: var(--spacing-sm);
    line-height: 1.4;
    display: -webkit-box;
    -webkit-line-clamp: 2;
    line-clamp: 2;
    -webkit-box-orient: vertical;
    overflow: hidden;
    height: 56px;
}

.product-manufacturer {
    font-size: var(--font-size-sm);
    color: var(--neutral-gray);
    font-style: italic;
    margin-bottom: var(--spacing-md);
}

.product-price {
    margin-bottom: var(--spacing-lg);
}

.current-price {
    font-size: var(--font-size-xl);
    font-weight: 700;
    color: var(--primary-red);
}

.product-actions {
    margin-top: auto;
}

.btn-add-cart {
    width: 100%;
    justify-content: center;
}

/* Pagination */
.pagination-wrapper-japanese {
    display: flex;
    justify-content: center;
    margin-top: var(--spacing-3xl);
}

.pagination-wrapper-japanese .pagination {
    display: flex;
    gap: var(--spacing-sm);
    list-style: none;
    margin: 0;
    padding: 0;
}

.pagination-wrapper-japanese .pagination li a,
.pagination-wrapper-japanese .pagination li span {
    display: flex;
    align-items: center;
    justify-content: center;
    width: 44px;
    height: 44px;
    border: 2px solid var(--light-gray);
    border-radius: var(--radius-md);
    color: var(--neutral-black);
    text-decoration: none;
    font-weight: 500;
    transition: all var(--transition-fast);
}

.pagination-wrapper-japanese .pagination li a:hover {
    border-color: var(--primary-red);
    color: var(--primary-red);
    transform: translateY(-2px);
}

.pagination-wrapper-japanese .pagination li.active span {
    background: var(--primary-red);
    border-color: var(--primary-red);
    color: var(--white);
}

/* Slick Carousel Japanese Style */
.newproduct-slider {
    position: relative;
    padding: 0 var(--spacing-xl);
}

.product-slide {
    padding: 0 var(--spacing-sm);
}

.slick-prev,
.slick-next {
    position: absolute;
    top: 50%;
    transform: translateY(-50%);
    background: var(--white);
    color: var(--primary-red);
    border: 2px solid var(--primary-red);
    border-radius: 50%;
    width: 50px;
    height: 50px;
    display: flex !important;
    align-items: center;
    justify-content: center;
    z-index: 10;
    cursor: pointer;
    font-size: var(--font-size-lg);
    transition: all var(--transition-normal);
    box-shadow: var(--shadow-md);
}

.slick-prev {
    left: -10px;
}

.slick-next {
    right: -10px;
}

.slick-prev:hover,
.slick-next:hover {
    background: var(--primary-red);
    color: var(--white);
    transform: translateY(-50%) scale(1.1);
    box-shadow: var(--shadow-lg);
}

.slick-prev:focus,
.slick-next:focus {
    outline: none;
}

.slick-slide {
    margin: 0 var(--spacing-sm);
}

.slick-list {
    margin: 0 calc(-1 * var(--spacing-sm));
}

.slick-track {
    display: flex;
    align-items: stretch;
}

.slick-disabled {
    opacity: 0.3;
    cursor: not-allowed;
}

.slick-disabled:hover {
    background: var(--white);
    color: var(--primary-red);
    transform: translateY(-50%);
}

/* Responsive Design */
@media (max-width: 992px) {
    .hero-section-japanese {
        padding: var(--spacing-2xl) 0;
        min-height: auto;
    }

    .hero-text {
        padding-right: 0;
        margin-bottom: var(--spacing-xl);
    }

    .hero-image-wrapper {
        width: 300px;
        height: 300px;
    }

    .hero-icon {
        font-size: 60px;
    }

    .feature-card {
        margin-bottom: var(--spacing-lg);
    }
}

@media (max-width: 768px) {
    .hero-main-title {
        font-size: var(--font-size-3xl);
    }

    .hero-paragraph {
        font-size: var(--font-size-base);
    }

    .hero-actions {
        justify-content: center;
    }

    .hero-image-wrapper {
        width: 250px;
        height: 250px;
    }

    .hero-icon {
        font-size: 50px;
    }

    .product-image-wrapper {
        height: 220px;
    }

    .product-name {
        font-size: var(--font-size-base);
        height: 48px;
    }

    .current-price {
        font-size: var(--font-size-lg);
    }

    .slick-prev {
        left: -5px;
    }

    .slick-next {
        right: -5px;
    }

    .slick-prev,
    .slick-next {
        width: 40px;
        height: 40px;
        font-size: var(--font-size-base);
    }
}

@media (max-width: 576px) {
    .hero-section-japanese {
        padding: var(--spacing-xl) 0;
    }

    .hero-main-title {
        font-size: var(--font-size-2xl);
    }

    .hero-actions {
        flex-direction: column;
        align-items: center;
    }

    .hero-actions .btn-japanese {
        width: 100%;
        max-width: 250px;
    }

    .hero-image-wrapper {
        width: 200px;
        height: 200px;
    }

    .hero-icon {
        font-size: 40px;
    }

    .features-section-japanese,
    .new-products-section-japanese,
    .all-products-section-japanese {
        padding: var(--spacing-2xl) 0;
    }

    .product-image-wrapper {
        height: 200px;
    }

    .product-name {
        font-size: var(--font-size-sm);
        height: 44px;
    }

    .current-price {
        font-size: var(--font-size-base);
    }

    .feature-icon {
        width: 60px;
        height: 60px;
    }

    .feature-icon i {
        font-size: var(--font-size-xl);
    }

    .newproduct-slider {
        padding: 0 var(--spacing-md);
    }
}
</style>

<!-- Slick Carousel CSS -->
<link rel="stylesheet" type="text/css" href="https://cdn.jsdelivr.net/npm/slick-carousel@1.8.1/slick/slick.css"/>
<link rel="stylesheet" type="text/css" href="https://cdn.jsdelivr.net/npm/slick-carousel@1.8.1/slick/slick-theme.css"/>

<!-- Slick Carousel JS -->
<script type="text/javascript" src="https://cdn.jsdelivr.net/npm/slick-carousel@1.8.1/slick/slick.min.js"></script>

<script>
$(document).ready(function(){
    // Initialize New Products Slider
    $('.newproduct-slider').slick({
        slidesToShow: 3,
        slidesToScroll: 1,
        infinite: true,
        arrows: true,
        autoplay: true,
        autoplaySpeed: 4000,
        pauseOnHover: true,
        pauseOnFocus: true,
        prevArrow: '<button type="button" class="slick-prev" aria-label="Previous"><i class="fas fa-chevron-left"></i></button>',
        nextArrow: '<button type="button" class="slick-next" aria-label="Next"><i class="fas fa-chevron-right"></i></button>',
        responsive: [
            {
                breakpoint: 992,
                settings: {
                    slidesToShow: 2,
                    slidesToScroll: 1
                }
            },
            {
                breakpoint: 768,
                settings: {
                    slidesToShow: 1,
                    slidesToScroll: 1,
                    arrows: true
                }
            }
        ]
    });

    // Smooth scrolling for anchor links
    $('a[href^="#"]').on('click', function(event) {
        var target = $(this.getAttribute('href'));
        if( target.length ) {
            event.preventDefault();
            $('html, body').stop().animate({
                scrollTop: target.offset().top - 100
            }, 1000);
        }
    });

    // Add to cart form handling
    $('.add-to-cart-form').on('submit', function(e) {
        e.preventDefault();

        var form = $(this);
        var button = form.find('.btn-add-cart');
        var originalText = button.html();

        // Show loading state
        button.html('<i class="fas fa-spinner fa-spin"></i> Đang thêm...');
        button.prop('disabled', true);

        // Submit form via AJAX
        $.ajax({
            url: form.attr('action'),
            method: 'POST',
            data: form.serialize(),
            success: function(response) {
                // Show success message
                button.html('<i class="fas fa-check"></i> Đã thêm!');
                button.removeClass('btn-primary').addClass('btn-success');

                // Update cart count if function exists
                if (typeof updateCartCount === 'function') {
                    updateCartCount();
                }

                // Reset button after 2 seconds
                setTimeout(function() {
                    button.html(originalText);
                    button.removeClass('btn-success').addClass('btn-primary');
                    button.prop('disabled', false);
                }, 2000);
            },
            error: function(xhr) {
                // Show error message
                button.html('<i class="fas fa-exclamation-triangle"></i> Lỗi!');
                button.removeClass('btn-primary').addClass('btn-danger');

                // Reset button after 2 seconds
                setTimeout(function() {
                    button.html(originalText);
                    button.removeClass('btn-danger').addClass('btn-primary');
                    button.prop('disabled', false);
                }, 2000);

                // Show error alert if response has message
                if (xhr.responseJSON && xhr.responseJSON.message) {
                    alert(xhr.responseJSON.message);
                }
            }
        });
    });

    // Lazy loading for images
    if ('IntersectionObserver' in window) {
        const imageObserver = new IntersectionObserver((entries, observer) => {
            entries.forEach(entry => {
                if (entry.isIntersecting) {
                    const img = entry.target;
                    img.src = img.dataset.src;
                    img.classList.remove('lazy');
                    imageObserver.unobserve(img);
                }
            });
        });

        document.querySelectorAll('img[data-src]').forEach(img => {
            imageObserver.observe(img);
        });
    }

    // Animation on scroll
    const observerOptions = {
        threshold: 0.1,
        rootMargin: '0px 0px -50px 0px'
    };

    const observer = new IntersectionObserver((entries) => {
        entries.forEach(entry => {
            if (entry.isIntersecting) {
                entry.target.style.opacity = '1';
                entry.target.style.transform = 'translateY(0)';
            }
        });
    }, observerOptions);

    // Observe elements for animation
    document.querySelectorAll('.product-card-japanese, .feature-card').forEach(el => {
        el.style.opacity = '0';
        el.style.transform = 'translateY(30px)';
        el.style.transition = 'opacity 0.6s ease, transform 0.6s ease';
        observer.observe(el);
    });
});
</script>

@endsection