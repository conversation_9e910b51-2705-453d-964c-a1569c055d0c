/* 
 * Japanese Style Theme for E-commerce
 * Inspired by Japanese minimalism, clean design, and harmony
 */

/* ===== ROOT VARIABLES ===== */
:root {
    /* Japanese Color Palette */
    --primary-red: #D32F2F;        /* <PERSON><PERSON> (Red) - Traditional Japanese red */
    --primary-blue: #1976D2;       /* <PERSON> (Indigo) - Deep blue */
    --accent-gold: #FFB300;        /* <PERSON><PERSON> (Gold) - Elegant accent */
    --neutral-black: #212121;      /* <PERSON><PERSON> (Black) - Deep black */
    --neutral-gray: #757575;       /* <PERSON> (Gray) - Balanced gray */
    --light-gray: #F5F5F5;         /* Light background */
    --white: #FFFFFF;              /* <PERSON><PERSON> (White) - Pure white */
    --success-green: #388E3C;      /* <PERSON><PERSON> (Green) - Natural green */
    --warning-orange: #F57C00;     /* <PERSON><PERSON> (Orange) - Warm orange */
    
    /* Typography */
    --font-primary: 'Noto Sans JP', 'Hiragino Kaku Gothic Pro', 'Yu Gothic', 'Meiryo', sans-serif;
    --font-secondary: 'Roboto', 'Arial', sans-serif;
    --font-size-xs: 0.75rem;       /* 12px */
    --font-size-sm: 0.875rem;      /* 14px */
    --font-size-base: 1rem;        /* 16px */
    --font-size-lg: 1.125rem;      /* 18px */
    --font-size-xl: 1.25rem;       /* 20px */
    --font-size-2xl: 1.5rem;       /* 24px */
    --font-size-3xl: 1.875rem;     /* 30px */
    --font-size-4xl: 2.25rem;      /* 36px */
    
    /* Spacing */
    --spacing-xs: 0.25rem;         /* 4px */
    --spacing-sm: 0.5rem;          /* 8px */
    --spacing-md: 1rem;            /* 16px */
    --spacing-lg: 1.5rem;          /* 24px */
    --spacing-xl: 2rem;            /* 32px */
    --spacing-2xl: 3rem;           /* 48px */
    --spacing-3xl: 4rem;           /* 64px */
    
    /* Border Radius */
    --radius-sm: 4px;
    --radius-md: 8px;
    --radius-lg: 12px;
    --radius-xl: 16px;
    --radius-full: 50%;
    
    /* Shadows */
    --shadow-sm: 0 1px 2px 0 rgba(0, 0, 0, 0.05);
    --shadow-md: 0 4px 6px -1px rgba(0, 0, 0, 0.1), 0 2px 4px -1px rgba(0, 0, 0, 0.06);
    --shadow-lg: 0 10px 15px -3px rgba(0, 0, 0, 0.1), 0 4px 6px -2px rgba(0, 0, 0, 0.05);
    --shadow-xl: 0 20px 25px -5px rgba(0, 0, 0, 0.1), 0 10px 10px -5px rgba(0, 0, 0, 0.04);
    
    /* Transitions */
    --transition-fast: 0.15s ease-in-out;
    --transition-normal: 0.3s ease-in-out;
    --transition-slow: 0.5s ease-in-out;
}

/* ===== GLOBAL STYLES ===== */
* {
    box-sizing: border-box;
    margin: 0;
    padding: 0;
}

html {
    scroll-behavior: smooth;
    font-size: 16px;
}

body {
    font-family: var(--font-primary);
    font-size: var(--font-size-base);
    line-height: 1.6;
    color: var(--neutral-black);
    background-color: var(--white);
    -webkit-font-smoothing: antialiased;
    -moz-osx-font-smoothing: grayscale;
}

/* ===== TYPOGRAPHY ===== */
h1, h2, h3, h4, h5, h6 {
    font-weight: 600;
    line-height: 1.3;
    margin-bottom: var(--spacing-md);
    color: var(--neutral-black);
}

h1 { font-size: var(--font-size-4xl); }
h2 { font-size: var(--font-size-3xl); }
h3 { font-size: var(--font-size-2xl); }
h4 { font-size: var(--font-size-xl); }
h5 { font-size: var(--font-size-lg); }
h6 { font-size: var(--font-size-base); }

p {
    margin-bottom: var(--spacing-md);
    color: var(--neutral-gray);
}

a {
    color: var(--primary-blue);
    text-decoration: none;
    transition: color var(--transition-fast);
}

a:hover {
    color: var(--primary-red);
    text-decoration: none;
}

/* ===== BUTTONS ===== */
.btn-japanese {
    display: inline-flex;
    align-items: center;
    justify-content: center;
    padding: var(--spacing-sm) var(--spacing-lg);
    font-size: var(--font-size-base);
    font-weight: 500;
    border: 2px solid transparent;
    border-radius: var(--radius-md);
    cursor: pointer;
    transition: all var(--transition-normal);
    text-decoration: none;
    min-height: 44px;
    gap: var(--spacing-sm);
}

.btn-primary {
    background-color: var(--primary-red);
    color: var(--white);
    border-color: var(--primary-red);
}

.btn-primary:hover {
    background-color: #B71C1C;
    border-color: #B71C1C;
    transform: translateY(-1px);
    box-shadow: var(--shadow-md);
}

.btn-secondary {
    background-color: var(--white);
    color: var(--primary-red);
    border-color: var(--primary-red);
}

.btn-secondary:hover {
    background-color: var(--primary-red);
    color: var(--white);
}

.btn-outline {
    background-color: transparent;
    color: var(--neutral-black);
    border-color: var(--neutral-gray);
}

.btn-outline:hover {
    background-color: var(--neutral-black);
    color: var(--white);
    border-color: var(--neutral-black);
}

/* ===== CARDS ===== */
.card-japanese {
    background: var(--white);
    border-radius: var(--radius-lg);
    box-shadow: var(--shadow-sm);
    transition: all var(--transition-normal);
    overflow: hidden;
    border: 1px solid rgba(0, 0, 0, 0.05);
}

.card-japanese:hover {
    box-shadow: var(--shadow-lg);
    transform: translateY(-2px);
}

.card-header {
    padding: var(--spacing-lg);
    border-bottom: 1px solid var(--light-gray);
    background-color: var(--white);
}

.card-body {
    padding: var(--spacing-lg);
}

.card-footer {
    padding: var(--spacing-lg);
    border-top: 1px solid var(--light-gray);
    background-color: var(--light-gray);
}

/* ===== FORMS ===== */
.form-group {
    margin-bottom: var(--spacing-lg);
}

.form-label {
    display: block;
    font-weight: 500;
    margin-bottom: var(--spacing-sm);
    color: var(--neutral-black);
    font-size: var(--font-size-sm);
}

.form-control {
    width: 100%;
    padding: var(--spacing-sm) var(--spacing-md);
    font-size: var(--font-size-base);
    border: 2px solid var(--light-gray);
    border-radius: var(--radius-md);
    transition: all var(--transition-fast);
    background-color: var(--white);
    min-height: 44px;
}

.form-control:focus {
    outline: none;
    border-color: var(--primary-blue);
    box-shadow: 0 0 0 3px rgba(25, 118, 210, 0.1);
}

.form-control::placeholder {
    color: var(--neutral-gray);
    opacity: 0.7;
}

/* ===== NAVIGATION ===== */
.navbar-japanese {
    background-color: var(--white);
    box-shadow: var(--shadow-sm);
    padding: var(--spacing-md) 0;
    position: sticky;
    top: 0;
    z-index: 1000;
}

.nav-link {
    color: var(--neutral-black);
    font-weight: 500;
    padding: var(--spacing-sm) var(--spacing-md);
    border-radius: var(--radius-sm);
    transition: all var(--transition-fast);
}

.nav-link:hover {
    color: var(--primary-red);
    background-color: rgba(211, 47, 47, 0.05);
}

.nav-link.active {
    color: var(--primary-red);
    background-color: rgba(211, 47, 47, 0.1);
}

/* ===== UTILITIES ===== */
.text-center { text-align: center; }
.text-left { text-align: left; }
.text-right { text-align: right; }

.mb-0 { margin-bottom: 0; }
.mb-1 { margin-bottom: var(--spacing-xs); }
.mb-2 { margin-bottom: var(--spacing-sm); }
.mb-3 { margin-bottom: var(--spacing-md); }
.mb-4 { margin-bottom: var(--spacing-lg); }
.mb-5 { margin-bottom: var(--spacing-xl); }

.mt-0 { margin-top: 0; }
.mt-1 { margin-top: var(--spacing-xs); }
.mt-2 { margin-top: var(--spacing-sm); }
.mt-3 { margin-top: var(--spacing-md); }
.mt-4 { margin-top: var(--spacing-lg); }
.mt-5 { margin-top: var(--spacing-xl); }

.p-0 { padding: 0; }
.p-1 { padding: var(--spacing-xs); }
.p-2 { padding: var(--spacing-sm); }
.p-3 { padding: var(--spacing-md); }
.p-4 { padding: var(--spacing-lg); }
.p-5 { padding: var(--spacing-xl); }

/* ===== RESPONSIVE DESIGN ===== */
@media (max-width: 768px) {
    :root {
        --font-size-4xl: 1.875rem;     /* 30px */
        --font-size-3xl: 1.5rem;       /* 24px */
        --font-size-2xl: 1.25rem;      /* 20px */
    }
    
    .btn-japanese {
        padding: var(--spacing-sm) var(--spacing-md);
        font-size: var(--font-size-sm);
    }
    
    .card-body,
    .card-header,
    .card-footer {
        padding: var(--spacing-md);
    }
}

/* ===== LOADING ANIMATION ===== */
.loading-spinner {
    display: inline-block;
    width: 20px;
    height: 20px;
    border: 2px solid var(--light-gray);
    border-radius: 50%;
    border-top-color: var(--primary-red);
    animation: spin 1s ease-in-out infinite;
}

@keyframes spin {
    to { transform: rotate(360deg); }
}

/* ===== JAPANESE AESTHETIC ELEMENTS ===== */
.divider-japanese {
    height: 1px;
    background: linear-gradient(to right, transparent, var(--neutral-gray), transparent);
    margin: var(--spacing-xl) 0;
}

.section-title-japanese {
    position: relative;
    text-align: center;
    margin-bottom: var(--spacing-2xl);
}

.section-title-japanese::after {
    content: '';
    position: absolute;
    bottom: -10px;
    left: 50%;
    transform: translateX(-50%);
    width: 60px;
    height: 3px;
    background: linear-gradient(to right, var(--primary-red), var(--accent-gold));
    border-radius: var(--radius-full);
}
