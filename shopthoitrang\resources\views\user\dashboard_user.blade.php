<!DOCTYPE html>
<html lang="vi">

<head>
    <meta charset="utf-8">
    <meta http-equiv="X-UA-Compatible" content="IE=edge">
    <meta name="viewport" content="width=device-width, initial-scale=1">

    <!-- SEO Meta Tags -->
    <title>@yield('title', 'Shop Thời Trang - Phong Cách Nhật Bản')</title>
    <meta name="description" content="@yield('description', 'Shop thời trang cao cấp với phong cách Nhật Bản. Sản phẩm chất lư<PERSON>, thiết kế tinh tế, giao hàng toàn quốc.')">
    <meta name="keywords" content="@yield('keywords', 'thời trang, nhật bản, quần áo, phụ kiện, chất lượng cao')">
    <meta name="author" content="Shop Thời Trang">
    <meta name="robots" content="index, follow">

    <!-- Open Graph Meta Tags -->
    <meta property="og:title" content="@yield('og_title', 'Shop Thời Trang - Phong Cách Nhật Bản')">
    <meta property="og:description" content="@yield('og_description', 'Shop thời trang cao cấp với phong cách Nhật Bản')">
    <meta property="og:image" content="@yield('og_image', asset('img/logo.png'))">
    <meta property="og:url" content="{{ url()->current() }}">
    <meta property="og:type" content="website">
    <meta property="og:site_name" content="Shop Thời Trang">

    <!-- Twitter Card Meta Tags -->
    <meta name="twitter:card" content="summary_large_image">
    <meta name="twitter:title" content="@yield('twitter_title', 'Shop Thời Trang - Phong Cách Nhật Bản')">
    <meta name="twitter:description" content="@yield('twitter_description', 'Shop thời trang cao cấp với phong cách Nhật Bản')">
    <meta name="twitter:image" content="@yield('twitter_image', asset('img/logo.png'))">

    <!-- Canonical URL -->
    <link rel="canonical" href="{{ url()->current() }}">

    <!-- Favicon -->
    <link rel="icon" type="image/x-icon" href="{{ asset('img/favicon.ico') }}">
    <link rel="apple-touch-icon" href="{{ asset('img/apple-touch-icon.png') }}">

    <!-- Google Fonts - Japanese Typography -->
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Noto+Sans+JP:wght@300;400;500;600;700&family=Roboto:wght@300;400;500;600;700&display=swap" rel="stylesheet">

    <!-- Bootstrap 5 -->
    <link href="https://cdn.jsdelivr.net/npm/bootstrap@5.3.3/dist/css/bootstrap.min.css" rel="stylesheet">

    <!-- Font Awesome -->
    <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/font-awesome/6.4.0/css/all.min.css">

    <!-- Slick Carousel -->
    <link type="text/css" rel="stylesheet" href="{{ asset('css/slick.css') }}" />
    <link type="text/css" rel="stylesheet" href="{{ asset('css/slick-theme.css') }}" />

    <!-- Japanese Theme CSS -->
    <link type="text/css" rel="stylesheet" href="{{ asset('css/japanese-theme.css') }}" />

    <!-- Custom Styles -->
    <link type="text/css" rel="stylesheet" href="{{ asset('css/style.css') }}" />

    <!-- jQuery -->
    <script src="https://code.jquery.com/jquery-3.6.0.min.js"></script>

    <!-- Bootstrap JS -->
    <script src="https://cdn.jsdelivr.net/npm/bootstrap@5.3.3/dist/js/bootstrap.bundle.min.js"></script>

    <!-- Slick Carousel JS -->
    <script src="{{ asset('js/slick.min.js') }}"></script>

    <!-- Structured Data -->
    <script type="application/ld+json">
    {
        "@context": "https://schema.org",
        "@type": "Store",
        "name": "Shop Thời Trang",
        "description": "Shop thời trang cao cấp với phong cách Nhật Bản",
        "url": "{{ url('/') }}",
        "logo": "{{ asset('img/logo.png') }}",
        "address": {
            "@type": "PostalAddress",
            "streetAddress": "1734 Stonecoal Road",
            "addressCountry": "VN"
        },
        "telephone": "+021-95-51-84",
        "email": "<EMAIL>"
    }
    </script>

</head>

<body>
    <!-- HEADER -->
    <header class="japanese-header">
        <!-- TOP HEADER -->
        <div class="top-header-japanese">
            <div class="container">
                <div class="row align-items-center">
                    <div class="col-md-6">
                        <div class="contact-info">
                            <span class="contact-item">
                                <i class="fas fa-phone-alt"></i>
                                <a href="tel:+021-95-51-84">+021-95-51-84</a>
                            </span>
                            <span class="contact-item">
                                <i class="fas fa-envelope"></i>
                                <a href="mailto:<EMAIL>"><EMAIL></a>
                            </span>
                        </div>
                    </div>
                    <div class="col-md-6">
                        <div class="top-header-right">
                            <span class="welcome-text">Chào mừng đến với Shop Thời Trang</span>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <!-- /TOP HEADER -->

        <!-- MAIN HEADER -->
        <div class="main-header-japanese">
            <div class="container">
                <div class="row align-items-center">
                    <!-- LOGO -->
                    <div class="col-lg-2 col-md-3 col-6">
                        <div class="logo-japanese">
                            <a href="{{ route('home.index') }}" class="logo-link">
                                <img src="{{ asset('./img/logo.png') }}" alt="Shop Thời Trang Logo" class="logo-img">
                                <span class="logo-text">Shop Thời Trang</span>
                            </a>
                        </div>
                    </div>
                    <!-- /LOGO -->

                    <!-- SEARCH BAR -->
                    <div class="col-lg-5 col-md-6 col-12 order-md-2 order-3">
                        <div class="search-japanese">
                            <form action="{{ route('user.searchProduct') }}" method="GET" class="search-form">
                                <div class="search-input-group">
                                    <input
                                        type="text"
                                        name="keyword"
                                        class="search-input"
                                        placeholder="Tìm kiếm sản phẩm..."
                                        aria-label="Tìm kiếm sản phẩm"
                                        required>
                                    <button class="search-btn" type="submit" aria-label="Tìm kiếm">
                                        <i class="fas fa-search"></i>
                                    </button>
                                </div>
                            </form>
                        </div>
                    </div>
                    <!-- /SEARCH BAR -->

                    <!-- NAVIGATION -->
                    <div class="col-lg-5 col-md-3 col-6 order-md-3 order-2">
                        <div class="header-nav-japanese">
                            <nav class="main-nav">
                                <ul class="nav-list">
                                    <li class="nav-item">
                                        <a href="{{ route('post.indexListPostUser') }}" class="nav-link">
                                            <i class="fas fa-newspaper"></i>
                                            <span class="nav-text">Bài viết</span>
                                        </a>
                                    </li>
                                    @if(Session::get('id_user'))
                                        <li class="nav-item dropdown">
                                            <a href="#" class="nav-link dropdown-toggle" data-bs-toggle="dropdown" aria-expanded="false">
                                                <i class="fas fa-industry"></i>
                                                <span class="nav-text">Nhà Sản Xuất</span>
                                            </a>
                                            <ul class="dropdown-menu dropdown-menu-japanese">
                                                @foreach ($manufacturers as $manufacturer)
                                                <li>
                                                    <a class="dropdown-item" href="{{ route('manufacturer.products', $manufacturer->id_manufacturer) }}">
                                                        {{ $manufacturer->name_manufacturer }}
                                                    </a>
                                                </li>
                                                @endforeach
                                            </ul>
                                        </li>
                                        <li class="nav-item">
                                            <a href="{{ route('cart.indexCart') }}" class="nav-link position-relative">
                                                <i class="fas fa-shopping-cart"></i>
                                                <span class="nav-text">Giỏ hàng</span>
                                                <span class="nav-badge cart-badge" id="header-cart-count">{{ $cartCount }}</span>
                                            </a>
                                        </li>
                                        <li class="nav-item">
                                            <a href="{{ route('order.orderIndex') }}" class="nav-link position-relative">
                                                <i class="fas fa-file-invoice"></i>
                                                <span class="nav-text">Đơn hàng</span>
                                                <span class="nav-badge order-badge" id="header-order-count">{{ $orderCount }}</span>
                                            </a>
                                        </li>
                                        <li class="nav-item">
                                            <a href="{{ route('signout') }}" class="nav-link">
                                                <i class="fas fa-sign-out-alt"></i>
                                                <span class="nav-text">Đăng xuất</span>
                                            </a>
                                        </li>
                                    @else
                                        <li class="nav-item">
                                            <a href="{{ route('user.indexlogin') }}" class="nav-link">
                                                <i class="fas fa-sign-in-alt"></i>
                                                <span class="nav-text">Đăng nhập</span>
                                            </a>
                                        </li>
                                    @endif
                                    <li class="nav-item mobile-menu-toggle">
                                        <a href="#" class="nav-link menu-toggle">
                                            <i class="fas fa-bars"></i>
                                            <span class="nav-text">Menu</span>
                                        </a>
                                    </li>
                                </ul>
                            </nav>
                        </div>
                    </div>
                    <!-- /NAVIGATION -->
                </div>
            </div>
        </div>
        <!-- /MAIN HEADER -->
    </header>
    <!-- Thêm phần hiển thị thông báo -->
    <div class="container mt-3">
        @if(session('success'))
            <div class="alert alert-success alert-dismissible fade show" role="alert">
                {{ session('success') }}
                <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
            </div>
        @endif

        @if(session('error'))
            <div class="alert alert-danger alert-dismissible fade show" role="alert">
                {{ session('error') }}
                <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
            </div>
        @endif

        @if($errors->any())
            <div class="alert alert-danger alert-dismissible fade show" role="alert">
                <ul class="mb-0">
                    @foreach($errors->all() as $error)
                        <li>{{ $error }}</li>
                    @endforeach
                </ul>
                <button type="button" class="btn-close" data-bs-dismiss="alert" aria-label="Close"></button>
            </div>
        @endif
    </div>
    @yield('content')

</body>
<style>
/* ===== JAPANESE HEADER STYLES ===== */
.japanese-header {
    position: sticky;
    top: 0;
    z-index: 1000;
    background: var(--white);
    box-shadow: var(--shadow-md);
}

/* Top Header */
.top-header-japanese {
    background: var(--light-gray);
    padding: var(--spacing-sm) 0;
    font-size: var(--font-size-sm);
    border-bottom: 1px solid rgba(0, 0, 0, 0.05);
}

.contact-info {
    display: flex;
    gap: var(--spacing-lg);
}

.contact-item {
    display: flex;
    align-items: center;
    gap: var(--spacing-sm);
    color: var(--neutral-gray);
}

.contact-item i {
    color: var(--primary-red);
    font-size: var(--font-size-xs);
}

.contact-item a {
    color: var(--neutral-gray);
    text-decoration: none;
    transition: color var(--transition-fast);
}

.contact-item a:hover {
    color: var(--primary-red);
}

.top-header-right {
    text-align: right;
}

.welcome-text {
    color: var(--neutral-gray);
    font-size: var(--font-size-sm);
    font-weight: 400;
}

/* Main Header */
.main-header-japanese {
    background: var(--white);
    padding: var(--spacing-md) 0;
}

/* Logo */
.logo-japanese {
    display: flex;
    align-items: center;
}

.logo-link {
    display: flex;
    align-items: center;
    gap: var(--spacing-sm);
    text-decoration: none;
    transition: transform var(--transition-fast);
}

.logo-link:hover {
    transform: scale(1.02);
}

.logo-img {
    height: 50px;
    width: auto;
    object-fit: contain;
}

.logo-text {
    font-size: var(--font-size-lg);
    font-weight: 600;
    color: var(--neutral-black);
    display: none;
}

/* Search */
.search-japanese {
    max-width: 500px;
    margin: 0 auto;
}

.search-form {
    width: 100%;
}

.search-input-group {
    position: relative;
    display: flex;
    width: 100%;
    border-radius: var(--radius-xl);
    overflow: hidden;
    box-shadow: var(--shadow-sm);
    border: 2px solid var(--light-gray);
    transition: all var(--transition-fast);
}

.search-input-group:focus-within {
    border-color: var(--primary-blue);
    box-shadow: 0 0 0 3px rgba(25, 118, 210, 0.1);
}

.search-input {
    flex: 1;
    padding: var(--spacing-sm) var(--spacing-md);
    border: none;
    outline: none;
    font-size: var(--font-size-base);
    background: var(--white);
    min-height: 44px;
}

.search-input::placeholder {
    color: var(--neutral-gray);
    opacity: 0.7;
}

.search-btn {
    padding: var(--spacing-sm) var(--spacing-lg);
    background: var(--primary-red);
    color: var(--white);
    border: none;
    cursor: pointer;
    transition: all var(--transition-fast);
    display: flex;
    align-items: center;
    justify-content: center;
    min-width: 50px;
}

.search-btn:hover {
    background: #B71C1C;
    transform: scale(1.05);
}

/* Navigation */
.header-nav-japanese {
    display: flex;
    justify-content: flex-end;
}

.main-nav {
    display: flex;
    align-items: center;
}

.nav-list {
    display: flex;
    align-items: center;
    gap: var(--spacing-sm);
    margin: 0;
    padding: 0;
    list-style: none;
}

.nav-item {
    position: relative;
}

.nav-link {
    display: flex;
    flex-direction: column;
    align-items: center;
    gap: 2px;
    padding: var(--spacing-sm);
    color: var(--neutral-black);
    text-decoration: none;
    border-radius: var(--radius-md);
    transition: all var(--transition-fast);
    min-width: 60px;
    text-align: center;
}

.nav-link:hover {
    color: var(--primary-red);
    background: rgba(211, 47, 47, 0.05);
    transform: translateY(-1px);
}

.nav-link i {
    font-size: var(--font-size-lg);
    margin-bottom: 2px;
}

.nav-text {
    font-size: var(--font-size-xs);
    font-weight: 500;
    white-space: nowrap;
}

.nav-badge {
    position: absolute;
    top: 2px;
    right: 2px;
    background: var(--primary-red);
    color: var(--white);
    border-radius: var(--radius-full);
    padding: 2px 6px;
    font-size: 10px;
    font-weight: 600;
    min-width: 18px;
    text-align: center;
    line-height: 1.2;
}

/* Dropdown */
.dropdown-menu-japanese {
    border: none;
    border-radius: var(--radius-lg);
    box-shadow: var(--shadow-lg);
    padding: var(--spacing-sm);
    margin-top: var(--spacing-sm);
    min-width: 200px;
}

.dropdown-menu-japanese .dropdown-item {
    padding: var(--spacing-sm) var(--spacing-md);
    border-radius: var(--radius-sm);
    transition: all var(--transition-fast);
    font-size: var(--font-size-sm);
}

.dropdown-menu-japanese .dropdown-item:hover {
    background: var(--primary-red);
    color: var(--white);
}

/* Mobile Menu Toggle */
.mobile-menu-toggle {
    display: none;
}

/* Responsive Design */
@media (max-width: 992px) {
    .logo-text {
        display: block;
    }

    .nav-text {
        display: none;
    }

    .nav-link {
        min-width: 40px;
    }

    .search-japanese {
        margin: var(--spacing-md) 0;
    }
}

@media (max-width: 768px) {
    .main-header-japanese .row > div {
        margin-bottom: var(--spacing-sm);
    }

    .contact-info {
        flex-direction: column;
        gap: var(--spacing-sm);
    }

    .top-header-right {
        text-align: left;
        margin-top: var(--spacing-sm);
    }

    .mobile-menu-toggle {
        display: block;
    }

    .nav-list {
        gap: var(--spacing-xs);
    }

    .search-input {
        font-size: var(--font-size-sm);
    }
}

@media (max-width: 576px) {
    .nav-list {
        flex-wrap: wrap;
        justify-content: center;
    }

    .logo-img {
        height: 40px;
    }

    .logo-text {
        font-size: var(--font-size-base);
    }
}
</style>

<script>
    // Cập nhật số lượng giỏ hàng và đơn hàng khi trang được tải
    $(document).ready(function() {
        updateCartCount();
        updateOrderCount();
    });

    // Hàm cập nhật số lượng sản phẩm trong giỏ hàng
    function updateCartCount() {
        $.ajax({
            url: "{{ route('cart.getCount') }}",
            method: 'GET',
            success: function(response) {
                $('#header-cart-count').text(response.count);
            }
        });
    }

    // Hàm cập nhật số lượng đơn hàng
    function updateOrderCount() {
        $.ajax({
            url: "{{ route('order.getCount') }}",
            method: 'GET',
            success: function(response) {
                $('#header-order-count').text(response.count);
            }
        });
    }

    // Cập nhật số lượng sau mỗi 30 giây
    setInterval(function() {
        updateCartCount();
        updateOrderCount();
    }, 30000);
</script>

</html>